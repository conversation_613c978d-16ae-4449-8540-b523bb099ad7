using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Milvus.Client;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement;

/// <summary>
/// 别名设置项
/// </summary>
public class AliasSettingItem
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string param { get; set; }

    /// <summary>
    /// 知识库GUID
    /// </summary>
    public string KnowledgeGUID { get; set; }
}

/// <summary>
/// 执行设置（包含aliasSetting）
/// </summary>
public class ExecutionSettingWithAlias
{
    /// <summary>
    /// 别名设置列表
    /// </summary>
    public List<AliasSettingItem> aliasSetting { get; set; }
}

/// <summary>
/// 基础数据修正参数助手
/// </summary>
public class BaseDataFixParamHelper
{
    private readonly ILogger<BaseDataFixParamHelper> _logger;
    private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
    private readonly KnowledgeRepository _knowledgeRepository;
    private readonly ModelInstanceRepostory _modelInstanceRepository;
    private readonly IMysoftContextFactory _mysoftContextFactory;
    private readonly IMilvusMemoryDomainService _milvusMemoryDomainService;
    private readonly Kernel _kernel;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="knowledgeFileSectionRepository">知识文件段仓储</param>
    /// <param name="knowledgeRepository">知识仓储</param>
    /// <param name="modelInstanceRepository">模型实例仓储</param>
    /// <param name="mysoftContextFactory">上下文工厂</param>
    /// <param name="milvusMemoryDomainService">向量库内存域服务</param>
    /// <param name="kernel">语义内核</param>
    public BaseDataFixParamHelper(
        ILogger<BaseDataFixParamHelper> logger,
        KnowledgeFileSectionRepository knowledgeFileSectionRepository,
        KnowledgeRepository knowledgeRepository,
        ModelInstanceRepostory modelInstanceRepository,
        IMysoftContextFactory mysoftContextFactory,
        IMilvusMemoryDomainService milvusMemoryDomainService,
        Kernel kernel)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _knowledgeFileSectionRepository = knowledgeFileSectionRepository ?? throw new ArgumentNullException(nameof(knowledgeFileSectionRepository));
        _knowledgeRepository = knowledgeRepository ?? throw new ArgumentNullException(nameof(knowledgeRepository));
        _modelInstanceRepository = modelInstanceRepository ?? throw new ArgumentNullException(nameof(modelInstanceRepository));
        _mysoftContextFactory = mysoftContextFactory ?? throw new ArgumentNullException(nameof(mysoftContextFactory));
        _milvusMemoryDomainService = milvusMemoryDomainService ?? throw new ArgumentNullException(nameof(milvusMemoryDomainService));
        _kernel = kernel ?? throw new ArgumentNullException(nameof(kernel));
    }

    /// <summary>
    /// 修正参数，根据aliasSetting配置查询向量库并替换参数值
    /// </summary>
    /// <param name="dict">参数字典</param>
    /// <param name="thirdId">第三方ID</param>
    /// <returns>修正后的参数字典</returns>
    public async Task<Dictionary<string, string>> FixParams(Dictionary<string, string> dict, string thirdId)
    {
        var startTime = DateTime.UtcNow;
        var logPrefix = $"[FixParams-{thirdId}]";

        _logger.LogInformation("{logPrefix} 开始参数修正，ThirdId: {thirdId}, 输入参数数量: {paramCount}",
            logPrefix, thirdId, dict?.Count ?? 0);

        if (dict == null || dict.Count == 0)
        {
            _logger.LogInformation("{logPrefix} 输入参数为空，跳过处理", logPrefix);
            return dict;
        }

        // 记录输入参数详情
        _logger.LogDebug("{logPrefix} 输入参数详情: {parameters}", logPrefix,
            string.Join(", ", dict.Select(kv => $"{kv.Key}={kv.Value}")));

        KnowledgeFileSectionEntity knowledgeFileSectionEntity = await _knowledgeFileSectionRepository.GetFirstAsync(f => f.ThirdId == thirdId);
        if (knowledgeFileSectionEntity == null)
        {
            _logger.LogWarning("{logPrefix} 未找到对应的KnowledgeFileSectionEntity记录", logPrefix);
            return dict;
        }

        if (string.IsNullOrWhiteSpace(knowledgeFileSectionEntity.ExecutionSetting))
        {
            _logger.LogInformation("{logPrefix} ExecutionSetting为空，无需处理", logPrefix);
            return dict;
        }

        _logger.LogDebug("{logPrefix} 找到ExecutionSetting: {executionSetting}", logPrefix, knowledgeFileSectionEntity.ExecutionSetting);

        var processedCount = 0;
        var successCount = 0;
        var failedCount = 0;

        try
        {
            // 解析ExecutionSetting JSON
            var executionSetting = JsonConvert.DeserializeObject<ExecutionSettingWithAlias>(knowledgeFileSectionEntity.ExecutionSetting);
            if (executionSetting?.aliasSetting == null || !executionSetting.aliasSetting.Any())
            {
                _logger.LogInformation("{logPrefix} ExecutionSetting中没有aliasSetting配置", logPrefix);
                return dict;
            }

            _logger.LogInformation("{logPrefix} 开始处理aliasSetting，共{count}个配置项", logPrefix, executionSetting.aliasSetting.Count);

            // 遍历aliasSetting配置
            foreach (var aliasSetting in executionSetting.aliasSetting)
            {
                var itemLogPrefix = $"{logPrefix}-{aliasSetting.param}";

                if (string.IsNullOrWhiteSpace(aliasSetting.param) || string.IsNullOrWhiteSpace(aliasSetting.KnowledgeGUID))
                {
                    _logger.LogWarning("{itemLogPrefix} 配置项无效，param: {param}, KnowledgeGUID: {knowledgeGuid}",
                        itemLogPrefix, aliasSetting.param, aliasSetting.KnowledgeGUID);
                    continue;
                }

                // 检查字典中是否存在对应的key
                if (!dict.ContainsKey(aliasSetting.param))
                {
                    _logger.LogWarning("{itemLogPrefix} 参数字典中不存在该参数，跳过处理", itemLogPrefix);
                    continue;
                }

                processedCount++;
                var paramStartTime = DateTime.UtcNow;

                try
                {
                    // 查询向量库获取数据，传入当前参数值进行相似度搜索
                    var currentParamValue = dict[aliasSetting.param];
                    _logger.LogInformation("{itemLogPrefix} 开始处理参数，原值: {originalValue}, KnowledgeGUID: {knowledgeGuid}",
                        itemLogPrefix, currentParamValue, aliasSetting.KnowledgeGUID);

                    var queryResult = await QueryVectorDatabaseByKnowledgeGuidAsync(aliasSetting.KnowledgeGUID, currentParamValue);
                    var paramElapsed = DateTime.UtcNow - paramStartTime;

                    if (!string.IsNullOrWhiteSpace(queryResult))
                    {
                        // 替换字典中的值
                        dict[aliasSetting.param] = queryResult;
                        successCount++;

                        _logger.LogInformation("{itemLogPrefix} 成功替换参数值，原值: {oldValue}, 新值: {newValue}, 耗时: {elapsed}ms",
                            itemLogPrefix, currentParamValue, queryResult, paramElapsed.TotalMilliseconds);
                    }
                    else
                    {
                        failedCount++;
                        _logger.LogWarning("{itemLogPrefix} 查询向量库返回空结果，保持原值: {originalValue}, 耗时: {elapsed}ms",
                            itemLogPrefix, currentParamValue, paramElapsed.TotalMilliseconds);
                    }
                }
                catch (Exception ex)
                {
                    failedCount++;
                    var paramElapsed = DateTime.UtcNow - paramStartTime;
                    _logger.LogError(ex, "{itemLogPrefix} 查询向量库异常，param: {param}, KnowledgeGUID: {knowledgeGuid}, 耗时: {elapsed}ms",
                        itemLogPrefix, aliasSetting.param, aliasSetting.KnowledgeGUID, paramElapsed.TotalMilliseconds);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{logPrefix} 解析ExecutionSetting失败: {executionSetting}", logPrefix, knowledgeFileSectionEntity.ExecutionSetting);
        }

        var totalElapsed = DateTime.UtcNow - startTime;
        _logger.LogInformation("{logPrefix} 参数修正完成，总耗时: {elapsed}ms, 处理: {processed}个, 成功: {success}个, 失败: {failed}个",
            logPrefix, totalElapsed.TotalMilliseconds, processedCount, successCount, failedCount);

        // 记录最终参数详情
        _logger.LogDebug("{logPrefix} 最终参数详情: {parameters}", logPrefix,
            string.Join(", ", dict.Select(kv => $"{kv.Key}={kv.Value}")));

        return dict;
    }

    /// <summary>
    /// 根据KnowledgeGUID查询向量库数据，通过相似度搜索找到最匹配的内容
    /// </summary>
    /// <param name="knowledgeGuid">知识库GUID</param>
    /// <param name="queryText">查询文本（参数值）</param>
    /// <returns>相似度最高的查询结果</returns>
    private async Task<string> QueryVectorDatabaseByKnowledgeGuidAsync(string knowledgeGuid, string queryText)
    {
        var startTime = DateTime.UtcNow;
        var logPrefix = $"[QueryVector-{knowledgeGuid[..8]}]";

        try
        {
            _logger.LogInformation("{logPrefix} 开始向量查询，KnowledgeGUID: {knowledgeGuid}, 查询文本长度: {textLength}, 查询文本: {queryText}",
                logPrefix, knowledgeGuid, queryText?.Length ?? 0, queryText);

            if (string.IsNullOrWhiteSpace(queryText))
            {
                _logger.LogWarning("{logPrefix} 查询文本为空，无法进行相似度搜索", logPrefix);
                return string.Empty;
            }

            // 从数据库获取对应的KnowledgeEntity
            var dbQueryStart = DateTime.UtcNow;
            Guid knGuid = Guid.Parse(knowledgeGuid);
            KnowledgeEntity knowledgeEntity = await _knowledgeRepository.GetFirstAsync(f => f.KnowledgeGUID == knGuid);
            var dbQueryElapsed = DateTime.UtcNow - dbQueryStart;

            if (knowledgeEntity == null || string.IsNullOrWhiteSpace(knowledgeEntity.Code))
            {
                _logger.LogWarning("{logPrefix} 无法找到KnowledgeGUID对应的知识库，耗时: {elapsed}ms", logPrefix, dbQueryElapsed.TotalMilliseconds);
                return string.Empty;
            }

            _logger.LogInformation("{logPrefix} 找到知识库，Code: {knowledgeCode}, 耗时: {elapsed}ms",
                logPrefix, knowledgeEntity.Code, dbQueryElapsed.TotalMilliseconds);

            // 获取对应的嵌入生成服务
            var modelQueryStart = DateTime.UtcNow;
            string serviceKey = await GetEmbeddingModelCodeByKnowledgeCode(knowledgeEntity.Code);
            var modelQueryElapsed = DateTime.UtcNow - modelQueryStart;

            _logger.LogInformation("{logPrefix} 获取向量模型完成，模型: {serviceKey}, 耗时: {elapsed}ms",
                logPrefix, serviceKey ?? "默认", modelQueryElapsed.TotalMilliseconds);

            var embeddingStart = DateTime.UtcNow;
            ITextEmbeddingGenerationService embeddingGenerationService = _kernel.GetRequiredService<ITextEmbeddingGenerationService>(serviceKey);

            // 生成查询文本的向量嵌入
            ReadOnlyMemory<float> queryEmbedding = await embeddingGenerationService.GenerateEmbeddingAsync(queryText, _kernel);
            var embeddingElapsed = DateTime.UtcNow - embeddingStart;

            _logger.LogInformation("{logPrefix} 成功生成查询向量，维度: {dimension}, 耗时: {elapsed}ms",
                logPrefix, queryEmbedding.Length, embeddingElapsed.TotalMilliseconds);

            // 使用向量相似度搜索查找最匹配的数据
            var searchStart = DateTime.UtcNow;
            var searchResults = new List<MemoryQueryResult>();

            _logger.LogInformation("{logPrefix} 开始向量相似度搜索，集合: {collection}", logPrefix, knowledgeEntity.Code);

            await foreach (var result in _milvusMemoryDomainService.SearchAsync(
                collection: knowledgeEntity.Code,
                queryEmbedding: queryEmbedding,
                limit: 1, // 只取相似度最高的一个结果
                minRelevanceScore: 0.0, // 不设置最小相似度阈值
                withEmbeddings: false,
                sourceTypeEnum: 1)) // FileSection类型
            {
                searchResults.Add(result);
            }

            var searchElapsed = DateTime.UtcNow - searchStart;

            if (!searchResults.Any())
            {
                _logger.LogWarning("{logPrefix} 未找到相似的数据，知识库: {knowledgeCode}, 搜索耗时: {elapsed}ms",
                    logPrefix, knowledgeEntity.Code, searchElapsed.TotalMilliseconds);
                return string.Empty;
            }

            // 获取相似度最高的结果
            var bestMatch = searchResults.First();
            _logger.LogInformation("{logPrefix} 找到最佳匹配，相似度: {relevance:F4}, ID: {id}, 搜索耗时: {elapsed}ms",
                logPrefix, bestMatch.Relevance, bestMatch.Metadata.Id, searchElapsed.TotalMilliseconds);

            // 从向量库搜索结果中获取ID，然后去gpt_knowledgefilesection表查询具体内容
            if (!Guid.TryParse(bestMatch.Metadata.Id, out var sectionGuid))
            {
                _logger.LogWarning("{logPrefix} 无法解析ID为Guid格式: {id}", logPrefix, bestMatch.Metadata.Id);
                return string.Empty;
            }

            // 从gpt_knowledgefilesection表获取具体内容
            var contentQueryStart = DateTime.UtcNow;
            var sectionEntity = await _knowledgeFileSectionRepository.GetFirstAsync(x =>
                x.KnowledgeFileSectionGUID == sectionGuid && x.Disable == 0);
            var contentQueryElapsed = DateTime.UtcNow - contentQueryStart;

            if (sectionEntity == null)
            {
                _logger.LogWarning("{logPrefix} 在gpt_knowledgefilesection表中未找到对应记录，ID: {id}, 查询耗时: {elapsed}ms",
                    logPrefix, bestMatch.Metadata.Id, contentQueryElapsed.TotalMilliseconds);
                return string.Empty;
            }

            if (string.IsNullOrWhiteSpace(sectionEntity.Content))
            {
                _logger.LogWarning("{logPrefix} 查询到的记录内容为空，ID: {id}, 标题: {title}, 查询耗时: {elapsed}ms",
                    logPrefix, bestMatch.Metadata.Id, sectionEntity.ParagraphTitle ?? "无标题", contentQueryElapsed.TotalMilliseconds);
                return string.Empty;
            }

            var totalElapsed = DateTime.UtcNow - startTime;
            _logger.LogInformation("{logPrefix} 查询成功，内容长度: {length}, 标题: {title}, 内容查询耗时: {contentElapsed}ms, 总耗时: {totalElapsed}ms",
                logPrefix, sectionEntity.Content.Length, sectionEntity.ParagraphTitle ?? "无标题",
                contentQueryElapsed.TotalMilliseconds, totalElapsed.TotalMilliseconds);

            // 记录内容预览（前100个字符）
            var contentPreview = sectionEntity.Content.Length > 100
                ? sectionEntity.Content.Substring(0, 100) + "..."
                : sectionEntity.Content;
            _logger.LogDebug("{logPrefix} 返回内容预览: {contentPreview}", logPrefix, contentPreview);

            return sectionEntity.Content;
        }
        catch (Exception ex)
        {
            var totalElapsed = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "{logPrefix} 查询向量库异常，KnowledgeGUID: {knowledgeGuid}, 查询文本: {queryText}, 总耗时: {elapsed}ms",
                logPrefix, knowledgeGuid, queryText, totalElapsed.TotalMilliseconds);
            return string.Empty;
        }
    }

    /// <summary>
    /// 根据知识库编码获取向量模型编码
    /// </summary>
    /// <param name="knowledgeCode">知识库编码</param>
    /// <returns>向量模型编码，如果没有配置则返回null（使用默认模型）</returns>
    private async Task<string> GetEmbeddingModelCodeByKnowledgeCode(string knowledgeCode)
    {
        var startTime = DateTime.UtcNow;
        var logPrefix = $"[GetEmbeddingModel-{knowledgeCode}]";

        try
        {
            _logger.LogDebug("{logPrefix} 开始获取向量模型编码", logPrefix);

            var knowledge = await _knowledgeRepository.FindAsync(f => f.Code == knowledgeCode);
            if (knowledge == null)
            {
                var elapsed = DateTime.UtcNow - startTime;
                _logger.LogWarning("{logPrefix} 未找到知识库记录，使用默认模型，耗时: {elapsed}ms", logPrefix, elapsed.TotalMilliseconds);
                return null; // 使用默认模型
            }

            if (string.IsNullOrEmpty(knowledge.EmbeddingModelCode))
            {
                var elapsed = DateTime.UtcNow - startTime;
                _logger.LogDebug("{logPrefix} 知识库未配置向量模型，使用默认模型，耗时: {elapsed}ms", logPrefix, elapsed.TotalMilliseconds);
                return null; // 使用默认模型
            }

            // 检查是否为默认模型实例
            int count = await _modelInstanceRepository.CountAsync(f => f.IsDefault && f.InstanceCode == knowledge.EmbeddingModelCode);
            var totalElapsed = DateTime.UtcNow - startTime;

            if (count > 0)
            {
                _logger.LogDebug("{logPrefix} 配置的是默认模型实例，返回null，模型编码: {modelCode}, 耗时: {elapsed}ms",
                    logPrefix, knowledge.EmbeddingModelCode, totalElapsed.TotalMilliseconds);
                return null; // 如果是默认模型，返回null
            }

            _logger.LogInformation("{logPrefix} 获取到自定义向量模型编码: {modelCode}, 耗时: {elapsed}ms",
                logPrefix, knowledge.EmbeddingModelCode, totalElapsed.TotalMilliseconds);
            return knowledge.EmbeddingModelCode;
        }
        catch (Exception ex)
        {
            var elapsed = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "{logPrefix} 获取向量模型编码异常，使用默认模型，耗时: {elapsed}ms", logPrefix, elapsed.TotalMilliseconds);
            return null; // 出错时使用默认模型
        }
    }
}