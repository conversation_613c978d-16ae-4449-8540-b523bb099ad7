using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Milvus.Client;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement;

/// <summary>
/// 别名设置项
/// </summary>
public class AliasSettingItem
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string param { get; set; }

    /// <summary>
    /// 知识库GUID
    /// </summary>
    public string KnowledgeGUID { get; set; }
}

/// <summary>
/// 执行设置（包含aliasSetting）
/// </summary>
public class ExecutionSettingWithAlias
{
    /// <summary>
    /// 别名设置列表
    /// </summary>
    public List<AliasSettingItem> aliasSetting { get; set; }
}

/// <summary>
/// 基础数据修正参数助手
/// </summary>
public class BaseDataFixParamHelper
{
    private readonly ILogger<BaseDataFixParamHelper> _logger;
    private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
    private readonly KnowledgeRepository _knowledgeRepository;
    private readonly IMysoftContextFactory _mysoftContextFactory;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="knowledgeFileSectionRepository">知识文件段仓储</param>
    /// <param name="knowledgeRepository">知识仓储</param>
    /// <param name="mysoftContextFactory">上下文工厂</param>
    public BaseDataFixParamHelper(
        ILogger<BaseDataFixParamHelper> logger,
        KnowledgeFileSectionRepository knowledgeFileSectionRepository,
        KnowledgeRepository knowledgeRepository,
        IMysoftContextFactory mysoftContextFactory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _knowledgeFileSectionRepository = knowledgeFileSectionRepository ?? throw new ArgumentNullException(nameof(knowledgeFileSectionRepository));
        _knowledgeRepository = knowledgeRepository ?? throw new ArgumentNullException(nameof(knowledgeRepository));
        _mysoftContextFactory = mysoftContextFactory ?? throw new ArgumentNullException(nameof(mysoftContextFactory));
    }

    /// <summary>
    /// 修正参数，根据aliasSetting配置查询向量库并替换参数值
    /// </summary>
    /// <param name="dict">参数字典</param>
    /// <param name="thirdId">第三方ID</param>
    /// <returns>修正后的参数字典</returns>
    public async Task<Dictionary<string, string>> FixParams(Dictionary<string, string> dict, string thirdId)
    {
        if (dict == null || dict.Count == 0)
        {
            return dict;
        }

        KnowledgeFileSectionEntity knowledgeFileSectionEntity = await _knowledgeFileSectionRepository.GetFirstAsync(f => f.ThirdId == thirdId);
        if (knowledgeFileSectionEntity == null || string.IsNullOrWhiteSpace(knowledgeFileSectionEntity.ExecutionSetting))
        {
            return dict;
        }

        try
        {
            // 解析ExecutionSetting JSON
            var executionSetting = JsonConvert.DeserializeObject<ExecutionSettingWithAlias>(knowledgeFileSectionEntity.ExecutionSetting);
            if (executionSetting?.aliasSetting == null || !executionSetting.aliasSetting.Any())
            {
                return dict;
            }

            _logger.LogInformation("[FixParams] 开始处理aliasSetting，共{count}个配置", executionSetting.aliasSetting.Count);

            // 遍历aliasSetting配置
            foreach (var aliasSetting in executionSetting.aliasSetting)
            {
                if (string.IsNullOrWhiteSpace(aliasSetting.param) || string.IsNullOrWhiteSpace(aliasSetting.KnowledgeGUID))
                {
                    continue;
                }

                // 检查字典中是否存在对应的key
                if (dict.ContainsKey(aliasSetting.param))
                {
                    try
                    {
                        // 查询向量库获取数据
                        var queryResult = await QueryVectorDatabaseByKnowledgeGuidAsync(aliasSetting.KnowledgeGUID);
                        if (!string.IsNullOrWhiteSpace(queryResult))
                        {
                            // 替换字典中的值
                            dict[aliasSetting.param] = queryResult;
                            _logger.LogInformation("[FixParams] 成功替换参数 {param} 的值，KnowledgeGUID: {knowledgeGuid}", aliasSetting.param, aliasSetting.KnowledgeGUID);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[FixParams] 查询向量库失败，param: {param}, KnowledgeGUID: {knowledgeGuid}", aliasSetting.param, aliasSetting.KnowledgeGUID);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[FixParams] 解析ExecutionSetting失败: {executionSetting}", knowledgeFileSectionEntity.ExecutionSetting);
        }

        return dict;
    }

    /// <summary>
    /// 根据KnowledgeGUID查询向量库数据
    /// </summary>
    /// <param name="knowledgeGuid">知识库GUID</param>
    /// <returns>查询结果</returns>
    private async Task<string> QueryVectorDatabaseByKnowledgeGuidAsync(string knowledgeGuid)
    {
        try
        {
            _logger.LogInformation("[QueryVectorDatabaseByKnowledgeGuidAsync] 开始查询KnowledgeGUID: {knowledgeGuid}", knowledgeGuid);

            // 从数据库获取对应的KnowledgeFileSectionEntity
            Guid knGuid = Guid.Parse(knowledgeGuid);
            KnowledgeEntity knowledgeEntity = await _knowledgeRepository.GetFirstAsync(f => f.KnowledgeGUID == knGuid);
            if (knowledgeEntity == null || string.IsNullOrWhiteSpace(knowledgeEntity.Code))
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 无法从Metadata获取knowledge_code");
                return string.Empty;
            }

            // 使用Milvus客户端查询向量库
            var mysoftContext = _mysoftContextFactory.GetMysoftContext();
            var milvusClient = mysoftContext.MemoryStore.CreateMilvusClient(mysoftContext.TenantCode);

            // 检查集合是否存在
            var collectionExists = await milvusClient.HasCollectionAsync(knowledgeEntity.Code);
            if (!collectionExists)
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 向量库集合不存在: {knowledgeCode}", knowledgeEntity.Code);
                return string.Empty;
            }

            var milvusCollection = milvusClient.GetCollection(knowledgeEntity.Code);

            // 构建查询表达式，查询指定ID的记录
            var queryExpression = $"id == \"{knowledgeGuid}\"";
            var queryParameters = new QueryParameters();
            queryParameters.OutputFields.Add("id");
            queryParameters.OutputFields.Add("metadata");
            queryParameters.ConsistencyLevel = ConsistencyLevel.Strong;

            // 执行查询
            var queryResult = await milvusCollection.QueryAsync(queryExpression, queryParameters);

            if (queryResult?.Any() != true)
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 向量库中未找到ID: {knowledgeGuid} 的记录", knowledgeGuid);
                return string.Empty;
            }

            // 提取查询结果 - 目前返回空字符串，因为原始实现也是TODO状态
            var idFieldData = queryResult.FirstOrDefault(f => f.FieldName == "id") as FieldData<string>;

            if (idFieldData?.Data?.Any() != true)
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 查询结果中未找到ID字段");
                return string.Empty;
            }

            // TODO: 这里需要根据实际业务需求返回具体的内容
            // 目前保持与原始实现一致，返回空字符串
            _logger.LogInformation("[QueryVectorDatabaseByKnowledgeGuidAsync] 查询完成，但返回内容待实现");
            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[QueryVectorDatabaseByKnowledgeGuidAsync] 查询向量库异常，KnowledgeGUID: {knowledgeGuid}", knowledgeGuid);
            return string.Empty;
        }
    }
}