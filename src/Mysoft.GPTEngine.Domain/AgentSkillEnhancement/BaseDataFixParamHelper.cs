using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Milvus.Client;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement;

/// <summary>
/// 别名设置项
/// </summary>
public class AliasSettingItem
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string param { get; set; }

    /// <summary>
    /// 知识库GUID
    /// </summary>
    public string KnowledgeGUID { get; set; }
}

/// <summary>
/// 执行设置（包含aliasSetting）
/// </summary>
public class ExecutionSettingWithAlias
{
    /// <summary>
    /// 别名设置列表
    /// </summary>
    public List<AliasSettingItem> aliasSetting { get; set; }
}

/// <summary>
/// 基础数据修正参数助手
/// </summary>
public class BaseDataFixParamHelper
{
    private readonly ILogger<BaseDataFixParamHelper> _logger;
    private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
    private readonly KnowledgeRepository _knowledgeRepository;
    private readonly ModelInstanceRepostory _modelInstanceRepository;
    private readonly IMysoftContextFactory _mysoftContextFactory;
    private readonly IMilvusMemoryDomainService _milvusMemoryDomainService;
    private readonly Kernel _kernel;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="knowledgeFileSectionRepository">知识文件段仓储</param>
    /// <param name="knowledgeRepository">知识仓储</param>
    /// <param name="modelInstanceRepository">模型实例仓储</param>
    /// <param name="mysoftContextFactory">上下文工厂</param>
    /// <param name="milvusMemoryDomainService">向量库内存域服务</param>
    /// <param name="kernel">语义内核</param>
    public BaseDataFixParamHelper(
        ILogger<BaseDataFixParamHelper> logger,
        KnowledgeFileSectionRepository knowledgeFileSectionRepository,
        KnowledgeRepository knowledgeRepository,
        ModelInstanceRepostory modelInstanceRepository,
        IMysoftContextFactory mysoftContextFactory,
        IMilvusMemoryDomainService milvusMemoryDomainService,
        Kernel kernel)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _knowledgeFileSectionRepository = knowledgeFileSectionRepository ?? throw new ArgumentNullException(nameof(knowledgeFileSectionRepository));
        _knowledgeRepository = knowledgeRepository ?? throw new ArgumentNullException(nameof(knowledgeRepository));
        _modelInstanceRepository = modelInstanceRepository ?? throw new ArgumentNullException(nameof(modelInstanceRepository));
        _mysoftContextFactory = mysoftContextFactory ?? throw new ArgumentNullException(nameof(mysoftContextFactory));
        _milvusMemoryDomainService = milvusMemoryDomainService ?? throw new ArgumentNullException(nameof(milvusMemoryDomainService));
        _kernel = kernel ?? throw new ArgumentNullException(nameof(kernel));
    }

    /// <summary>
    /// 修正参数，根据aliasSetting配置查询向量库并替换参数值
    /// </summary>
    /// <param name="dict">参数字典</param>
    /// <param name="thirdId">第三方ID</param>
    /// <returns>修正后的参数字典</returns>
    public async Task<Dictionary<string, string>> FixParams(Dictionary<string, string> dict, string thirdId)
    {
        if (dict == null || dict.Count == 0)
        {
            return dict;
        }

        KnowledgeFileSectionEntity knowledgeFileSectionEntity = await _knowledgeFileSectionRepository.GetFirstAsync(f => f.ThirdId == thirdId);
        if (knowledgeFileSectionEntity == null || string.IsNullOrWhiteSpace(knowledgeFileSectionEntity.ExecutionSetting))
        {
            return dict;
        }

        try
        {
            // 解析ExecutionSetting JSON
            var executionSetting = JsonConvert.DeserializeObject<ExecutionSettingWithAlias>(knowledgeFileSectionEntity.ExecutionSetting);
            if (executionSetting?.aliasSetting == null || !executionSetting.aliasSetting.Any())
            {
                return dict;
            }

            _logger.LogInformation("[FixParams] 开始处理aliasSetting，共{count}个配置", executionSetting.aliasSetting.Count);

            // 遍历aliasSetting配置
            foreach (var aliasSetting in executionSetting.aliasSetting)
            {
                if (string.IsNullOrWhiteSpace(aliasSetting.param) || string.IsNullOrWhiteSpace(aliasSetting.KnowledgeGUID))
                {
                    continue;
                }

                // 检查字典中是否存在对应的key
                if (dict.ContainsKey(aliasSetting.param))
                {
                    try
                    {
                        // 查询向量库获取数据，传入当前参数值进行相似度搜索
                        var currentParamValue = dict[aliasSetting.param];
                        var queryResult = await QueryVectorDatabaseByKnowledgeGuidAsync(aliasSetting.KnowledgeGUID, currentParamValue);
                        if (!string.IsNullOrWhiteSpace(queryResult))
                        {
                            // 替换字典中的值
                            dict[aliasSetting.param] = queryResult;
                            _logger.LogInformation("[FixParams] 成功替换参数 {param} 的值，原值: {oldValue}, 新值: {newValue}, KnowledgeGUID: {knowledgeGuid}",
                                aliasSetting.param, currentParamValue, queryResult, aliasSetting.KnowledgeGUID);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[FixParams] 查询向量库失败，param: {param}, KnowledgeGUID: {knowledgeGuid}", aliasSetting.param, aliasSetting.KnowledgeGUID);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[FixParams] 解析ExecutionSetting失败: {executionSetting}", knowledgeFileSectionEntity.ExecutionSetting);
        }

        return dict;
    }

    /// <summary>
    /// 根据KnowledgeGUID查询向量库数据，通过相似度搜索找到最匹配的内容
    /// </summary>
    /// <param name="knowledgeGuid">知识库GUID</param>
    /// <param name="queryText">查询文本（参数值）</param>
    /// <returns>相似度最高的查询结果</returns>
    private async Task<string> QueryVectorDatabaseByKnowledgeGuidAsync(string knowledgeGuid, string queryText)
    {
        try
        {
            _logger.LogInformation("[QueryVectorDatabaseByKnowledgeGuidAsync] 开始查询KnowledgeGUID: {knowledgeGuid}, 查询文本: {queryText}", knowledgeGuid, queryText);

            if (string.IsNullOrWhiteSpace(queryText))
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 查询文本为空，无法进行相似度搜索");
                return string.Empty;
            }

            // 从数据库获取对应的KnowledgeEntity
            Guid knGuid = Guid.Parse(knowledgeGuid);
            KnowledgeEntity knowledgeEntity = await _knowledgeRepository.GetFirstAsync(f => f.KnowledgeGUID == knGuid);
            if (knowledgeEntity == null || string.IsNullOrWhiteSpace(knowledgeEntity.Code))
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 无法找到KnowledgeGUID对应的知识库: {knowledgeGuid}", knowledgeGuid);
                return string.Empty;
            }

            // 获取对应的嵌入生成服务
            string serviceKey = await GetEmbeddingModelCodeByKnowledgeCode(knowledgeEntity.Code);
            _logger.LogInformation("[QueryVectorDatabaseByKnowledgeGuidAsync] 使用向量模型: {serviceKey}", serviceKey ?? "默认");

            ITextEmbeddingGenerationService embeddingGenerationService = _kernel.GetRequiredService<ITextEmbeddingGenerationService>(serviceKey);

            // 生成查询文本的向量嵌入
            ReadOnlyMemory<float> queryEmbedding = await embeddingGenerationService.GenerateEmbeddingAsync(queryText, _kernel);
            _logger.LogInformation("[QueryVectorDatabaseByKnowledgeGuidAsync] 成功生成查询向量，维度: {dimension}", queryEmbedding.Length);

            // 使用向量相似度搜索查找最匹配的数据
            var searchResults = new List<MemoryQueryResult>();
            await foreach (var result in _milvusMemoryDomainService.SearchAsync(
                collection: knowledgeEntity.Code,
                queryEmbedding: queryEmbedding,
                limit: 1, // 只取相似度最高的一个结果
                minRelevanceScore: 0.0, // 不设置最小相似度阈值
                withEmbeddings: false,
                sourceTypeEnum: 1)) // FileSection类型
            {
                searchResults.Add(result);
            }

            if (!searchResults.Any())
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 未找到相似的数据，知识库: {knowledgeCode}", knowledgeEntity.Code);
                return string.Empty;
            }

            // 获取相似度最高的结果
            var bestMatch = searchResults.First();
            _logger.LogInformation("[QueryVectorDatabaseByKnowledgeGuidAsync] 找到最佳匹配，相似度: {relevance}, ID: {id}",
                bestMatch.Relevance, bestMatch.Metadata.Id);

            // 从结果中提取文本内容
            // 优先使用 Text 字段，如果为空则使用 Description 字段
            string resultText = !string.IsNullOrWhiteSpace(bestMatch.Metadata.Text)
                ? bestMatch.Metadata.Text
                : bestMatch.Metadata.Description;

            if (string.IsNullOrWhiteSpace(resultText))
            {
                _logger.LogWarning("[QueryVectorDatabaseByKnowledgeGuidAsync] 查询结果中没有有效的文本内容");
                return string.Empty;
            }

            _logger.LogInformation("[QueryVectorDatabaseByKnowledgeGuidAsync] 查询成功，返回文本长度: {length}", resultText.Length);
            return resultText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[QueryVectorDatabaseByKnowledgeGuidAsync] 查询向量库异常，KnowledgeGUID: {knowledgeGuid}, 查询文本: {queryText}", knowledgeGuid, queryText);
            return string.Empty;
        }
    }

    /// <summary>
    /// 根据知识库编码获取向量模型编码
    /// </summary>
    /// <param name="knowledgeCode">知识库编码</param>
    /// <returns>向量模型编码，如果没有配置则返回null（使用默认模型）</returns>
    private async Task<string> GetEmbeddingModelCodeByKnowledgeCode(string knowledgeCode)
    {
        try
        {
            var knowledge = await _knowledgeRepository.FindAsync(f => f.Code == knowledgeCode);
            if (knowledge == null || string.IsNullOrEmpty(knowledge.EmbeddingModelCode))
            {
                return null; // 使用默认模型
            }

            // 检查是否为默认模型实例
            int count = await _modelInstanceRepository.CountAsync(f => f.IsDefault && f.InstanceCode == knowledge.EmbeddingModelCode);
            if (count > 0)
            {
                return null; // 如果是默认模型，返回null
            }

            return knowledge.EmbeddingModelCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[GetEmbeddingModelCodeByKnowledgeCode] 获取向量模型编码异常，knowledgeCode: {knowledgeCode}", knowledgeCode);
            return null; // 出错时使用默认模型
        }
    }
}